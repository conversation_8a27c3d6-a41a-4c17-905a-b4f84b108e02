import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';

// Mock components for testing
@Component({ template: 'Landing Page' })
class MockLandingPageComponent { }

@Component({ template: 'Prompt Content' })
class MockPromptContentComponent { }

@Component({ template: 'Code Window' })
class MockCodeWindowComponent { }

// Mock services
const mockRecentProjectService = {
  getProjectById: jasmine.createSpy('getProjectById').and.returnValue(of({
    project: { project_id: 'test-id', project_name: 'Test Project' }
  }))
};

const mockToastService = {
  error: jasmine.createSpy('error'),
  info: jasmine.createSpy('info')
};

const mockUuidService = {
  isValidProjectId: jasmine.createSpy('isValidProjectId').and.returnValue(true)
};

const mockCardSelectionService = {
  hasCardBeenSelected: jasmine.createSpy('hasCardBeenSelected').and.returnValue(true),
  setCardSelected: jasmine.createSpy('setCardSelected')
};

describe('Routing Integration Tests', () => {
  let router: Router;
  let location: Location;
  let fixture: any;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([
          // Main routes
          {
            path: 'experience/main',
            component: MockLandingPageComponent
          },
          
          // UI Design routes
          {
            path: 'experience/ui-design/new',
            component: MockPromptContentComponent
          },
          {
            path: 'experience/ui-design/:projectId',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/ui-design/:projectId/edit',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/ui-design/:projectId/preview',
            component: MockCodeWindowComponent
          },
          
          // Application routes
          {
            path: 'experience/application/new',
            component: MockPromptContentComponent
          },
          {
            path: 'experience/application/:projectId',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/application/:projectId/edit',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/application/:projectId/preview',
            component: MockCodeWindowComponent
          },
          
          // Project routes
          {
            path: 'experience/projects/:projectId',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/projects/:projectId/edit',
            component: MockCodeWindowComponent
          },
          {
            path: 'experience/projects/:projectId/share',
            component: MockCodeWindowComponent
          },
          
          // Legacy redirects
          {
            path: 'experience/prompt',
            redirectTo: 'experience/application/new'
          },
          {
            path: 'experience/generate-application/prompt',
            redirectTo: 'experience/application/new'
          },
          {
            path: 'experience/generate-ui-design/prompt',
            redirectTo: 'experience/ui-design/new'
          },
          
          // Default redirects
          {
            path: '',
            redirectTo: 'experience/main',
            pathMatch: 'full'
          },
          {
            path: '**',
            redirectTo: 'experience/main'
          }
        ])
      ],
      declarations: [
        MockLandingPageComponent,
        MockPromptContentComponent,
        MockCodeWindowComponent
      ],
      providers: [
        { provide: 'RecentProjectService', useValue: mockRecentProjectService },
        { provide: 'ToastService', useValue: mockToastService },
        { provide: 'UuidService', useValue: mockUuidService },
        { provide: 'CardSelectionService', useValue: mockCardSelectionService }
      ]
    }).compileComponents();

    router = TestBed.inject(Router);
    location = TestBed.inject(Location);
    fixture = TestBed.createComponent(MockLandingPageComponent);
  });

  describe('Basic Navigation', () => {
    it('should navigate to main page by default', async () => {
      await router.navigate(['']);
      expect(location.path()).toBe('/experience/main');
    });

    it('should redirect unknown routes to main page', async () => {
      await router.navigate(['/unknown/route']);
      expect(location.path()).toBe('/experience/main');
    });

    it('should navigate to UI design new page', async () => {
      await router.navigate(['/experience/ui-design/new']);
      expect(location.path()).toBe('/experience/ui-design/new');
    });

    it('should navigate to application new page', async () => {
      await router.navigate(['/experience/application/new']);
      expect(location.path()).toBe('/experience/application/new');
    });
  });

  describe('Project Routes', () => {
    const testProjectId = '12345678-1234-1234-1234-123456789012';

    it('should navigate to UI design project view', async () => {
      await router.navigate(['/experience/ui-design', testProjectId]);
      expect(location.path()).toBe(`/experience/ui-design/${testProjectId}`);
    });

    it('should navigate to UI design project edit', async () => {
      await router.navigate(['/experience/ui-design', testProjectId, 'edit']);
      expect(location.path()).toBe(`/experience/ui-design/${testProjectId}/edit`);
    });

    it('should navigate to UI design project preview', async () => {
      await router.navigate(['/experience/ui-design', testProjectId, 'preview']);
      expect(location.path()).toBe(`/experience/ui-design/${testProjectId}/preview`);
    });

    it('should navigate to application project view', async () => {
      await router.navigate(['/experience/application', testProjectId]);
      expect(location.path()).toBe(`/experience/application/${testProjectId}`);
    });

    it('should navigate to application project edit', async () => {
      await router.navigate(['/experience/application', testProjectId, 'edit']);
      expect(location.path()).toBe(`/experience/application/${testProjectId}/edit`);
    });

    it('should navigate to application project preview', async () => {
      await router.navigate(['/experience/application', testProjectId, 'preview']);
      expect(location.path()).toBe(`/experience/application/${testProjectId}/preview`);
    });

    it('should navigate to generic project view', async () => {
      await router.navigate(['/experience/projects', testProjectId]);
      expect(location.path()).toBe(`/experience/projects/${testProjectId}`);
    });

    it('should navigate to generic project edit', async () => {
      await router.navigate(['/experience/projects', testProjectId, 'edit']);
      expect(location.path()).toBe(`/experience/projects/${testProjectId}/edit`);
    });

    it('should navigate to generic project share', async () => {
      await router.navigate(['/experience/projects', testProjectId, 'share']);
      expect(location.path()).toBe(`/experience/projects/${testProjectId}/share`);
    });
  });

  describe('Legacy Route Redirects', () => {
    it('should redirect legacy prompt route to application new', async () => {
      await router.navigate(['/experience/prompt']);
      expect(location.path()).toBe('/experience/application/new');
    });

    it('should redirect legacy generate-application prompt route', async () => {
      await router.navigate(['/experience/generate-application/prompt']);
      expect(location.path()).toBe('/experience/application/new');
    });

    it('should redirect legacy generate-ui-design prompt route', async () => {
      await router.navigate(['/experience/generate-ui-design/prompt']);
      expect(location.path()).toBe('/experience/ui-design/new');
    });
  });

  describe('URL Parameter Handling', () => {
    const testProjectId = '12345678-1234-1234-1234-123456789012';

    it('should handle query parameters correctly', async () => {
      await router.navigate(['/experience/ui-design', testProjectId], {
        queryParams: { tab: 'code', theme: 'dark' }
      });
      
      expect(location.path()).toBe(`/experience/ui-design/${testProjectId}?tab=code&theme=dark`);
    });

    it('should handle fragments correctly', async () => {
      await router.navigate(['/experience/application', testProjectId], {
        fragment: 'section1'
      });
      
      expect(location.path()).toBe(`/experience/application/${testProjectId}#section1`);
    });

    it('should handle both query parameters and fragments', async () => {
      await router.navigate(['/experience/projects', testProjectId, 'edit'], {
        queryParams: { mode: 'advanced' },
        fragment: 'editor'
      });
      
      expect(location.path()).toBe(`/experience/projects/${testProjectId}/edit?mode=advanced#editor`);
    });
  });

  describe('Route State Preservation', () => {
    it('should preserve state during navigation', async () => {
      // Navigate to main page
      await router.navigate(['/experience/main']);
      expect(location.path()).toBe('/experience/main');

      // Navigate to new UI design
      await router.navigate(['/experience/ui-design/new']);
      expect(location.path()).toBe('/experience/ui-design/new');

      // Navigate to project view
      const projectId = '12345678-1234-1234-1234-123456789012';
      await router.navigate(['/experience/ui-design', projectId]);
      expect(location.path()).toBe(`/experience/ui-design/${projectId}`);

      // Navigate to edit mode
      await router.navigate(['/experience/ui-design', projectId, 'edit']);
      expect(location.path()).toBe(`/experience/ui-design/${projectId}/edit`);
    });
  });

  describe('Error Handling', () => {
    it('should handle navigation errors gracefully', async () => {
      try {
        await router.navigate(['/experience/invalid/route/structure']);
        // Should redirect to main page
        expect(location.path()).toBe('/experience/main');
      } catch (error) {
        // Navigation errors should be handled gracefully
        expect(error).toBeDefined();
      }
    });

    it('should handle malformed project IDs', async () => {
      await router.navigate(['/experience/projects/invalid-project-id']);
      // Should still navigate but guards would handle validation
      expect(location.path()).toBe('/experience/projects/invalid-project-id');
    });
  });

  describe('Performance Considerations', () => {
    it('should handle rapid navigation changes', async () => {
      const projectId = '12345678-1234-1234-1234-123456789012';
      
      // Simulate rapid navigation
      const navigationPromises = [
        router.navigate(['/experience/main']),
        router.navigate(['/experience/ui-design/new']),
        router.navigate(['/experience/ui-design', projectId]),
        router.navigate(['/experience/ui-design', projectId, 'edit']),
        router.navigate(['/experience/ui-design', projectId, 'preview'])
      ];

      await Promise.all(navigationPromises);
      
      // Should end up at the last navigation
      expect(location.path()).toBe(`/experience/ui-design/${projectId}/preview`);
    });

    it('should handle concurrent navigation attempts', async () => {
      const projectId1 = '12345678-1234-1234-1234-123456789012';
      const projectId2 = '87654321-4321-4321-4321-210987654321';
      
      // Start multiple navigations simultaneously
      const nav1 = router.navigate(['/experience/ui-design', projectId1]);
      const nav2 = router.navigate(['/experience/application', projectId2]);
      
      await Promise.all([nav1, nav2]);
      
      // One of them should succeed (typically the last one)
      const finalPath = location.path();
      expect(
        finalPath === `/experience/ui-design/${projectId1}` ||
        finalPath === `/experience/application/${projectId2}`
      ).toBe(true);
    });
  });
});
