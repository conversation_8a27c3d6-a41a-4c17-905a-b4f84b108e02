import { Injectable } from '@angular/core';

/**
 * ENTERPRISE: Route configuration service for centralized route management
 * Provides type-safe route generation and validation for the experience studio
 */
@Injectable({
  providedIn: 'root'
})
export class RouteConfigService {

  // ===== ROUTE CONSTANTS =====
  private readonly BASE_PATH = '/experience';
  
  private readonly ROUTE_PATTERNS = {
    HOME: `${this.BASE_PATH}/main`,
    
    // Project routes
    PROJECT_DIRECT: `${this.BASE_PATH}/projects/:projectId`,
    PROJECT_EDIT: `${this.BASE_PATH}/projects/:projectId/edit`,
    PROJECT_SHARE: `${this.BASE_PATH}/projects/:projectId/share`,
    
    // UI Design routes
    UI_DESIGN_NEW: `${this.BASE_PATH}/ui-design/new`,
    UI_DESIGN_PROJECT: `${this.BASE_PATH}/ui-design/:projectId`,
    UI_DESIGN_PREVIEW: `${this.BASE_PATH}/ui-design/:projectId/preview`,
    UI_DESIGN_EDIT: `${this.BASE_PATH}/ui-design/:projectId/edit`,
    
    // Application routes
    APPLICATION_NEW: `${this.BASE_PATH}/application/new`,
    APPLICATION_PROJECT: `${this.BASE_PATH}/application/:projectId`,
    APPLICATION_PREVIEW: `${this.BASE_PATH}/application/:projectId/preview`,
    APPLICATION_EDIT: `${this.BASE_PATH}/application/:projectId/edit`,
  } as const;

  // ===== ROUTE BUILDERS =====

  /**
   * Get home route
   */
  getHomeRoute(): string {
    return this.ROUTE_PATTERNS.HOME;
  }

  /**
   * Build project route
   */
  getProjectRoute(projectId: string, action?: 'edit' | 'share'): string {
    const baseRoute = this.ROUTE_PATTERNS.PROJECT_DIRECT.replace(':projectId', projectId);
    return action ? `${baseRoute}/${action}` : baseRoute;
  }

  /**
   * Build UI design route
   */
  getUIDesignRoute(projectId?: string, action?: 'preview' | 'edit'): string {
    if (!projectId) {
      return this.ROUTE_PATTERNS.UI_DESIGN_NEW;
    }
    
    const baseRoute = this.ROUTE_PATTERNS.UI_DESIGN_PROJECT.replace(':projectId', projectId);
    return action ? `${baseRoute}/${action}` : baseRoute;
  }

  /**
   * Build application route
   */
  getApplicationRoute(projectId?: string, action?: 'preview' | 'edit'): string {
    if (!projectId) {
      return this.ROUTE_PATTERNS.APPLICATION_NEW;
    }
    
    const baseRoute = this.ROUTE_PATTERNS.APPLICATION_PROJECT.replace(':projectId', projectId);
    return action ? `${baseRoute}/${action}` : baseRoute;
  }

  // ===== ROUTE VALIDATION =====

  /**
   * Validate if a route is a project route
   */
  isProjectRoute(route: string): boolean {
    return route.includes('/projects/') || 
           route.includes('/ui-design/') || 
           route.includes('/application/');
  }

  /**
   * Extract project ID from route
   */
  extractProjectId(route: string): string | null {
    const patterns = [
      /\/projects\/([^\/]+)/,
      /\/ui-design\/([^\/]+)/,
      /\/application\/([^\/]+)/
    ];
    
    for (const pattern of patterns) {
      const match = route.match(pattern);
      if (match && match[1] !== 'new') {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * Extract project type from route
   */
  extractProjectType(route: string): 'ui-design' | 'application' | 'generic' | null {
    if (route.includes('/ui-design/')) {
      return 'ui-design';
    } else if (route.includes('/application/')) {
      return 'application';
    } else if (route.includes('/projects/')) {
      return 'generic';
    }
    
    return null;
  }

  /**
   * Extract action from route
   */
  extractAction(route: string): 'view' | 'edit' | 'preview' | 'share' | 'new' | null {
    if (route.includes('/new')) {
      return 'new';
    } else if (route.includes('/edit')) {
      return 'edit';
    } else if (route.includes('/preview')) {
      return 'preview';
    } else if (route.includes('/share')) {
      return 'share';
    } else if (this.extractProjectId(route)) {
      return 'view';
    }
    
    return null;
  }

  // ===== LEGACY ROUTE MAPPING =====

  /**
   * Map legacy routes to new enterprise routes
   */
  mapLegacyRoute(legacyRoute: string): string {
    const legacyMappings: Record<string, string> = {
      '/experience/prompt': this.ROUTE_PATTERNS.APPLICATION_NEW,
      '/experience/code-preview': this.ROUTE_PATTERNS.APPLICATION_NEW,
      '/experience/generate-application/prompt': this.ROUTE_PATTERNS.APPLICATION_NEW,
      '/experience/generate-application/code-preview': this.ROUTE_PATTERNS.APPLICATION_NEW,
      '/experience/generate-ui-design/prompt': this.ROUTE_PATTERNS.UI_DESIGN_NEW,
      '/experience/generate-ui-design/code-preview': this.ROUTE_PATTERNS.UI_DESIGN_NEW,
    };

    // Handle project-specific legacy routes
    const projectMatch = legacyRoute.match(/\/generate-application\/code-preview\/projects\/([^\/]+)/);
    if (projectMatch) {
      return this.getProjectRoute(projectMatch[1]);
    }

    return legacyMappings[legacyRoute] || legacyRoute;
  }

  // ===== ROUTE METADATA =====

  /**
   * Get route metadata for analytics and tracking
   */
  getRouteMetadata(route: string): {
    type: string;
    projectId?: string;
    action?: string;
    category: string;
  } {
    const projectId = this.extractProjectId(route);
    const projectType = this.extractProjectType(route);
    const action = this.extractAction(route);

    return {
      type: projectType || 'unknown',
      projectId: projectId || undefined,
      action: action || undefined,
      category: this.isProjectRoute(route) ? 'project' : 'navigation'
    };
  }

  // ===== URL GENERATION HELPERS =====

  /**
   * Generate shareable URL for a project
   */
  generateShareableUrl(projectId: string, projectType?: 'ui-design' | 'application'): string {
    const baseUrl = window.location.origin;
    
    if (projectType) {
      return `${baseUrl}${this.getUIDesignRoute(projectId)}`;
    }
    
    return `${baseUrl}${this.getProjectRoute(projectId)}`;
  }

  /**
   * Generate deep link URL with parameters
   */
  generateDeepLink(
    projectType: 'ui-design' | 'application',
    projectId?: string,
    action?: 'preview' | 'edit',
    queryParams?: Record<string, string>
  ): string {
    let route: string;

    if (projectType === 'ui-design') {
      route = this.getUIDesignRoute(projectId, action);
    } else {
      route = this.getApplicationRoute(projectId, action);
    }

    if (queryParams && Object.keys(queryParams).length > 0) {
      const params = new URLSearchParams(queryParams);
      route += `?${params.toString()}`;
    }

    return route;
  }

  // ===== ENHANCED URL GENERATION =====

  /**
   * Generate QR code friendly short URL
   */
  generateShortUrl(projectId: string, projectType?: 'ui-design' | 'application'): string {
    const baseUrl = window.location.origin;
    const shortId = projectId.substring(0, 8);

    if (projectType) {
      return `${baseUrl}/p/${projectType.charAt(0)}/${shortId}`;
    }

    return `${baseUrl}/p/${shortId}`;
  }

  /**
   * Generate embeddable URL for iframes
   */
  generateEmbedUrl(projectId: string, options?: {
    theme?: 'light' | 'dark';
    hideNavigation?: boolean;
    autoplay?: boolean;
  }): string {
    const baseUrl = window.location.origin;
    const route = this.getProjectRoute(projectId, 'preview');
    const params = new URLSearchParams();

    params.set('embed', 'true');

    if (options?.theme) {
      params.set('theme', options.theme);
    }

    if (options?.hideNavigation) {
      params.set('hideNav', 'true');
    }

    if (options?.autoplay) {
      params.set('autoplay', 'true');
    }

    return `${baseUrl}${route}?${params.toString()}`;
  }

  /**
   * Generate API endpoint URL for project data
   */
  generateApiUrl(endpoint: string, projectId?: string): string {
    const baseApiUrl = '/api/v1'; // Configure based on environment

    if (projectId) {
      return `${baseApiUrl}/${endpoint}/${projectId}`;
    }

    return `${baseApiUrl}/${endpoint}`;
  }

  /**
   * Generate webhook URL for project events
   */
  generateWebhookUrl(projectId: string, eventType: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/webhooks/projects/${projectId}/events/${eventType}`;
  }

  /**
   * Generate download URL for project assets
   */
  generateDownloadUrl(projectId: string, assetType: 'code' | 'assets' | 'preview'): string {
    return this.generateApiUrl(`projects/${projectId}/download/${assetType}`);
  }

  /**
   * Generate collaboration URL for team sharing
   */
  generateCollaborationUrl(projectId: string, permissions: 'view' | 'edit' | 'admin'): string {
    const baseUrl = window.location.origin;
    const route = this.getProjectRoute(projectId);
    const token = this.generateCollaborationToken(projectId, permissions);

    return `${baseUrl}${route}?collab=${token}`;
  }

  /**
   * Generate collaboration token (placeholder - implement proper token generation)
   */
  private generateCollaborationToken(projectId: string, permissions: string): string {
    // TODO: Implement proper JWT or similar token generation
    const payload = { projectId, permissions, timestamp: Date.now() };
    return btoa(JSON.stringify(payload));
  }

  /**
   * Parse collaboration token
   */
  parseCollaborationToken(token: string): { projectId: string; permissions: string; timestamp: number } | null {
    try {
      const decoded = atob(token);
      return JSON.parse(decoded);
    } catch {
      return null;
    }
  }

  /**
   * Generate social sharing URLs
   */
  generateSocialShareUrls(projectId: string, projectName: string): {
    twitter: string;
    linkedin: string;
    facebook: string;
    email: string;
  } {
    const projectUrl = this.generateShareableUrl(projectId);
    const encodedUrl = encodeURIComponent(projectUrl);
    const encodedTitle = encodeURIComponent(`Check out my ${projectName} project`);
    const encodedDescription = encodeURIComponent('Created with Experience Studio');

    return {
      twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`
    };
  }

  /**
   * Validate URL format and security
   */
  validateUrl(url: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    try {
      const urlObj = new URL(url);

      // Check protocol
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        issues.push('Invalid protocol - only HTTP and HTTPS are allowed');
      }

      // Check for suspicious patterns
      const suspiciousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /<script/i,
        /onload=/i
      ];

      if (suspiciousPatterns.some(pattern => pattern.test(url))) {
        issues.push('URL contains potentially malicious content');
      }

      // Check length
      if (url.length > 2048) {
        issues.push('URL is too long (max 2048 characters)');
      }

    } catch {
      issues.push('Invalid URL format');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}
