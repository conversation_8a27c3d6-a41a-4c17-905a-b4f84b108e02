import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter, map, startWith } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouteConfigService } from './route-config.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Route-based state management service with Angular 19+ Signals
 * Manages application state based on current route and navigation context
 */
export interface RouteState {
  currentRoute: string;
  routeType: string;
  projectId?: string;
  projectType?: 'ui-design' | 'application' | 'generic';
  action?: 'view' | 'edit' | 'preview' | 'share' | 'new';
  isProjectRoute: boolean;
  isNewProjectRoute: boolean;
  isEditRoute: boolean;
  isPreviewRoute: boolean;
  canEdit: boolean;
  canShare: boolean;
  canDelete: boolean;
  metadata: Record<string, any>;
}

@Injectable({
  providedIn: 'root'
})
export class RouteStateService {
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly routeConfigService = inject(RouteConfigService);
  private readonly logger = createLogger('RouteStateService');

  // ENHANCED: Core route state signals
  private readonly currentRoute = signal<string>('');
  private readonly routeParams = signal<Record<string, string>>({});
  private readonly routeData = signal<Record<string, any>>({});
  private readonly queryParams = signal<Record<string, string>>({});

  // ENHANCED: Computed route state
  readonly routeState = computed<RouteState>(() => {
    const route = this.currentRoute();
    const params = this.routeParams();
    const data = this.routeData();
    
    return this.buildRouteState(route, params, data);
  });

  // ENHANCED: Specific computed properties for common use cases
  readonly projectId = computed(() => this.routeState().projectId);
  readonly projectType = computed(() => this.routeState().projectType);
  readonly currentAction = computed(() => this.routeState().action);
  readonly isProjectRoute = computed(() => this.routeState().isProjectRoute);
  readonly isEditMode = computed(() => this.routeState().isEditRoute);
  readonly canEdit = computed(() => this.routeState().canEdit);
  readonly canShare = computed(() => this.routeState().canShare);

  // ENHANCED: Navigation history
  private readonly navigationHistory = signal<string[]>([]);
  readonly canGoBack = computed(() => this.navigationHistory().length > 1);
  readonly previousRoute = computed(() => {
    const history = this.navigationHistory();
    return history.length > 1 ? history[history.length - 2] : null;
  });

  constructor() {
    this.initializeRouteTracking();
    this.setupRouteEffects();
  }

  /**
   * Initialize route tracking with Angular 19+ patterns
   */
  private initializeRouteTracking(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.extractRouteData()),
      startWith(this.extractRouteData()),
      takeUntilDestroyed()
    ).subscribe(({ route, params, data, queryParams }) => {
      this.updateRouteState(route, params, data, queryParams);
      this.updateNavigationHistory(route);
    });
  }

  /**
   * Setup reactive effects for route state changes
   */
  private setupRouteEffects(): void {
    // Log route state changes in development
    effect(() => {
      const state = this.routeState();
      this.logger.info('Route state updated:', state);
    });

    // Track route analytics
    effect(() => {
      const state = this.routeState();
      this.trackRouteAnalytics(state);
    });
  }

  /**
   * Extract complete route data from activated route tree
   */
  private extractRouteData(): {
    route: string;
    params: Record<string, string>;
    data: Record<string, any>;
    queryParams: Record<string, string>;
  } {
    let route = this.activatedRoute;
    let routePath = '';
    let allParams = {};
    let allData = {};
    let allQueryParams = {};

    // Traverse the route tree
    while (route) {
      if (route.snapshot.url.length > 0) {
        routePath += '/' + route.snapshot.url.map(segment => segment.path).join('/');
      }
      
      allParams = { ...allParams, ...route.snapshot.params };
      allData = { ...allData, ...route.snapshot.data };
      allQueryParams = { ...allQueryParams, ...route.snapshot.queryParams };
      
      route = route.firstChild;
    }

    return {
      route: routePath || this.router.url.split('?')[0],
      params: allParams,
      data: allData,
      queryParams: allQueryParams
    };
  }

  /**
   * Update route state signals
   */
  private updateRouteState(
    route: string,
    params: Record<string, string>,
    data: Record<string, any>,
    queryParams: Record<string, string>
  ): void {
    this.currentRoute.set(route);
    this.routeParams.set(params);
    this.routeData.set(data);
    this.queryParams.set(queryParams);
  }

  /**
   * Update navigation history
   */
  private updateNavigationHistory(route: string): void {
    const history = this.navigationHistory();
    const newHistory = [...history, route];
    
    // Keep only last 10 routes
    if (newHistory.length > 10) {
      newHistory.shift();
    }
    
    this.navigationHistory.set(newHistory);
  }

  /**
   * Build comprehensive route state
   */
  private buildRouteState(
    route: string,
    params: Record<string, string>,
    data: Record<string, any>
  ): RouteState {
    const projectId = params['projectId'];
    const routeType = data['routeType'] || 'unknown';
    
    // Extract project type from route
    const projectType = this.extractProjectType(route, data);
    
    // Extract action from route
    const action = this.extractAction(route, routeType);
    
    // Determine route characteristics
    const isProjectRoute = !!projectId || this.routeConfigService.isProjectRoute(route);
    const isNewProjectRoute = action === 'new';
    const isEditRoute = action === 'edit';
    const isPreviewRoute = action === 'preview';
    
    // Determine permissions (simplified - would be more complex in real app)
    const canEdit = isProjectRoute && !isNewProjectRoute;
    const canShare = isProjectRoute && !isNewProjectRoute;
    const canDelete = isProjectRoute && !isNewProjectRoute && isEditRoute;

    return {
      currentRoute: route,
      routeType,
      projectId,
      projectType,
      action,
      isProjectRoute,
      isNewProjectRoute,
      isEditRoute,
      isPreviewRoute,
      canEdit,
      canShare,
      canDelete,
      metadata: data
    };
  }

  /**
   * Extract project type from route
   */
  private extractProjectType(route: string, data: Record<string, any>): 'ui-design' | 'application' | 'generic' | undefined {
    // Try route-based detection first
    if (route.includes('/ui-design/')) {
      return 'ui-design';
    } else if (route.includes('/application/')) {
      return 'application';
    } else if (route.includes('/projects/')) {
      return 'generic';
    }
    
    // Try data-based detection
    const cardType = data['cardType'];
    if (cardType === 'Generate UI Design') {
      return 'ui-design';
    } else if (cardType === 'Generate Application') {
      return 'application';
    }
    
    return undefined;
  }

  /**
   * Extract action from route
   */
  private extractAction(route: string, routeType: string): 'view' | 'edit' | 'preview' | 'share' | 'new' | undefined {
    if (route.includes('/new') || routeType.includes('new')) {
      return 'new';
    } else if (route.includes('/edit') || routeType.includes('edit')) {
      return 'edit';
    } else if (route.includes('/preview') || routeType.includes('preview')) {
      return 'preview';
    } else if (route.includes('/share') || routeType.includes('share')) {
      return 'share';
    } else if (this.routeConfigService.extractProjectId(route)) {
      return 'view';
    }
    
    return undefined;
  }

  /**
   * Track route analytics (placeholder for real analytics)
   */
  private trackRouteAnalytics(state: RouteState): void {
    // TODO: Implement actual analytics tracking
    this.logger.debug('Route analytics:', {
      route: state.currentRoute,
      type: state.routeType,
      projectType: state.projectType,
      action: state.action
    });
  }

  // ===== PUBLIC API =====

  /**
   * Get current route state
   */
  getCurrentState(): RouteState {
    return this.routeState();
  }

  /**
   * Check if current route matches pattern
   */
  isCurrentRoute(pattern: string): boolean {
    return this.currentRoute().includes(pattern);
  }

  /**
   * Get current project context
   */
  getProjectContext(): { id?: string; type?: string; action?: string } {
    const state = this.routeState();
    return {
      id: state.projectId,
      type: state.projectType,
      action: state.action
    };
  }

  /**
   * Navigate with state preservation
   */
  navigateWithState(route: string[], queryParams?: Record<string, string>): void {
    this.router.navigate(route, { queryParams });
  }

  /**
   * Go back to previous route
   */
  goBack(): void {
    const previous = this.previousRoute();
    if (previous) {
      this.router.navigate([previous]);
    } else {
      this.router.navigate(['/experience/main']);
    }
  }

  /**
   * Get route-specific configuration
   */
  getRouteConfig(): {
    showBreadcrumbs: boolean;
    showBackButton: boolean;
    showProjectActions: boolean;
    pageTitle: string;
  } {
    const state = this.routeState();
    
    return {
      showBreadcrumbs: !state.isNewProjectRoute,
      showBackButton: state.isProjectRoute,
      showProjectActions: state.isProjectRoute && !state.isNewProjectRoute,
      pageTitle: this.generatePageTitle(state)
    };
  }

  /**
   * Generate page title based on route state
   */
  private generatePageTitle(state: RouteState): string {
    if (state.isNewProjectRoute) {
      const type = state.projectType === 'ui-design' ? 'UI Design' : 'Application';
      return `New ${type}`;
    }
    
    if (state.projectId) {
      const type = state.projectType === 'ui-design' ? 'UI Design' : 'Application';
      const action = state.action === 'edit' ? 'Edit' : 
                    state.action === 'preview' ? 'Preview' : 
                    state.action === 'share' ? 'Share' : '';
      return `${action} ${type} Project`.trim();
    }
    
    return 'Experience Studio';
  }

  /**
   * Subscribe to route state changes
   */
  onRouteStateChange(callback: (state: RouteState) => void): void {
    effect(() => {
      callback(this.routeState());
    });
  }
}
