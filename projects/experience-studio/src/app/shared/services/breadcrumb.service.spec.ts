import { TestBed } from '@angular/core/testing';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { BreadcrumbService } from './breadcrumb.service';
import { RouteConfigService } from './route-config.service';

describe('BreadcrumbService', () => {
  let service: BreadcrumbService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;
  let mockRouteConfigService: jasmine.SpyObj<RouteConfigService>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate'], {
      events: of(new NavigationEnd(1, '/experience/main', '/experience/main')),
      url: '/experience/main'
    });

    const routeConfigSpy = jasmine.createSpyObj('RouteConfigService', ['extractProjectId']);

    // Mock ActivatedRoute with nested structure
    mockActivatedRoute = {
      snapshot: {
        url: [{ path: 'experience' }, { path: 'main' }],
        data: {},
        params: {},
        queryParams: {}
      },
      firstChild: null
    };

    TestBed.configureTestingModule({
      providers: [
        BreadcrumbService,
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: RouteConfigService, useValue: routeConfigSpy }
      ]
    });

    service = TestBed.inject(BreadcrumbService);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRouteConfigService = TestBed.inject(RouteConfigService) as jasmine.SpyObj<RouteConfigService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Breadcrumb Generation', () => {
    it('should generate home breadcrumb for main route', () => {
      // Simulate main route
      mockRouter.url = '/experience/main';
      
      const breadcrumbs = service.breadcrumbs();
      
      expect(breadcrumbs).toEqual([
        {
          label: 'Home',
          route: '/experience/main',
          icon: 'home',
          isActive: true
        }
      ]);
    });

    it('should generate breadcrumbs for UI design new route', () => {
      mockRouter.url = '/experience/ui-design/new';
      
      // Mock the route data extraction
      spyOn(service as any, 'getRouteData').and.returnValue({
        route: '/experience/ui-design/new',
        data: { routeType: 'ui-design-new' },
        params: {}
      });

      const breadcrumbs = service.breadcrumbs();
      
      expect(breadcrumbs.length).toBe(3);
      expect(breadcrumbs[0].label).toBe('Home');
      expect(breadcrumbs[1].label).toBe('UI Design');
      expect(breadcrumbs[2].label).toBe('New');
      expect(breadcrumbs[2].isActive).toBe(true);
    });

    it('should generate breadcrumbs for project route with ID', () => {
      const projectId = '12345678-1234-1234-1234-123456789012';
      mockRouter.url = `/experience/projects/${projectId}`;
      
      spyOn(service as any, 'getRouteData').and.returnValue({
        route: `/experience/projects/${projectId}`,
        data: {},
        params: { projectId }
      });

      spyOn(service as any, 'isProjectId').and.returnValue(true);
      spyOn(service as any, 'getProjectName').and.returnValue('Test Project');

      const breadcrumbs = service.breadcrumbs();
      
      expect(breadcrumbs.length).toBe(3);
      expect(breadcrumbs[0].label).toBe('Home');
      expect(breadcrumbs[1].label).toBe('Projects');
      expect(breadcrumbs[2].label).toBe('Test Project');
      expect(breadcrumbs[2].metadata?.projectId).toBe(projectId);
    });

    it('should generate breadcrumbs for application edit route', () => {
      const projectId = '12345678-1234-1234-1234-123456789012';
      mockRouter.url = `/experience/application/${projectId}/edit`;
      
      spyOn(service as any, 'getRouteData').and.returnValue({
        route: `/experience/application/${projectId}/edit`,
        data: {},
        params: { projectId }
      });

      spyOn(service as any, 'isProjectId').and.returnValue(true);
      spyOn(service as any, 'getProjectName').and.returnValue('My App');

      const breadcrumbs = service.breadcrumbs();
      
      expect(breadcrumbs.length).toBe(4);
      expect(breadcrumbs[0].label).toBe('Home');
      expect(breadcrumbs[1].label).toBe('Application');
      expect(breadcrumbs[2].label).toBe('My App');
      expect(breadcrumbs[3].label).toBe('Edit');
      expect(breadcrumbs[3].isActive).toBe(true);
    });
  });

  describe('Navigation Methods', () => {
    it('should navigate to breadcrumb route', () => {
      const breadcrumb = {
        label: 'Test',
        route: '/experience/test'
      };

      service.navigateTo(breadcrumb);
      
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/experience/test']);
    });

    it('should not navigate if breadcrumb has no route', () => {
      const breadcrumb = {
        label: 'Test'
      };

      service.navigateTo(breadcrumb);
      
      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });

    it('should go back to previous breadcrumb', () => {
      // Mock breadcrumbs with multiple items
      spyOn(service, 'breadcrumbs').and.returnValue([
        { label: 'Home', route: '/experience/main' },
        { label: 'Projects', route: '/experience/projects' },
        { label: 'Current', isActive: true }
      ]);

      service.goBack();
      
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/experience/projects']);
    });

    it('should navigate to home', () => {
      service.goHome();
      
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/experience/main']);
    });
  });

  describe('Utility Methods', () => {
    it('should get current page title', () => {
      spyOn(service, 'breadcrumbs').and.returnValue([
        { label: 'Home', route: '/experience/main' },
        { label: 'Current Page', isActive: true }
      ]);

      const title = service.getCurrentPageTitle();
      
      expect(title).toBe('Current Page');
    });

    it('should return default title for empty breadcrumbs', () => {
      spyOn(service, 'breadcrumbs').and.returnValue([]);

      const title = service.getCurrentPageTitle();
      
      expect(title).toBe('Experience Studio');
    });

    it('should get breadcrumb path as string', () => {
      spyOn(service, 'breadcrumbs').and.returnValue([
        { label: 'Home' },
        { label: 'Projects' },
        { label: 'Current' }
      ]);

      const path = service.getBreadcrumbPath();
      
      expect(path).toBe('Home > Projects > Current');
    });

    it('should check if current route matches pattern', () => {
      mockRouter.url = '/experience/ui-design/test-id';
      
      spyOn(service as any, 'currentRoute').and.returnValue('/experience/ui-design/test-id');

      expect(service.isCurrentRoute('ui-design')).toBe(true);
      expect(service.isCurrentRoute('application')).toBe(false);
    });
  });

  describe('Project ID Detection', () => {
    it('should identify valid UUID as project ID', () => {
      const uuid = '12345678-1234-1234-1234-123456789012';
      const result = (service as any).isProjectId(uuid);
      
      expect(result).toBe(true);
    });

    it('should identify valid project ID with prefix', () => {
      const projectId = 'proj-12345678-1234-1234-1234-123456789012';
      const result = (service as any).isProjectId(projectId);
      
      expect(result).toBe(true);
    });

    it('should reject invalid project ID format', () => {
      const invalidId = 'not-a-uuid';
      const result = (service as any).isProjectId(invalidId);
      
      expect(result).toBe(false);
    });

    it('should reject "new" as project ID', () => {
      const result = (service as any).isProjectId('new');
      
      expect(result).toBe(false);
    });
  });

  describe('Project Name Resolution', () => {
    it('should get project name from resolved data', () => {
      const data = {
        projectData: {
          project: {
            project_name: 'Test Project'
          }
        }
      };

      const name = (service as any).getProjectName('test-id', data);
      
      expect(name).toBe('Test Project');
    });

    it('should get project name from metadata', () => {
      const data = {
        metadata: {
          title: 'Metadata Project'
        }
      };

      const name = (service as any).getProjectName('test-id', data);
      
      expect(name).toBe('Metadata Project');
    });

    it('should return null if no project name found', () => {
      const data = {};

      const name = (service as any).getProjectName('test-id', data);
      
      expect(name).toBeNull();
    });

    it('should ignore default title from metadata', () => {
      const data = {
        metadata: {
          title: 'Experience Studio'
        }
      };

      const name = (service as any).getProjectName('test-id', data);
      
      expect(name).toBeNull();
    });
  });

  describe('Route Building', () => {
    it('should build project route for projects segment', () => {
      const route = (service as any).buildProjectRoute(['projects', 'test-id'], 'test-id');
      
      expect(route).toBe('/experience/projects/test-id');
    });

    it('should build UI design route for ui-design segment', () => {
      const route = (service as any).buildProjectRoute(['ui-design', 'test-id'], 'test-id');
      
      expect(route).toBe('/experience/ui-design/test-id');
    });

    it('should build application route for application segment', () => {
      const route = (service as any).buildProjectRoute(['application', 'test-id'], 'test-id');
      
      expect(route).toBe('/experience/application/test-id');
    });

    it('should default to projects route for unknown segment', () => {
      const route = (service as any).buildProjectRoute(['unknown', 'test-id'], 'test-id');
      
      expect(route).toBe('/experience/projects/test-id');
    });
  });

  describe('Segment Label Formatting', () => {
    it('should format single word segment', () => {
      const formatted = (service as any).formatSegmentLabel('projects');
      
      expect(formatted).toBe('Projects');
    });

    it('should format hyphenated segment', () => {
      const formatted = (service as any).formatSegmentLabel('ui-design');
      
      expect(formatted).toBe('Ui Design');
    });

    it('should format multi-hyphenated segment', () => {
      const formatted = (service as any).formatSegmentLabel('some-long-segment-name');
      
      expect(formatted).toBe('Some Long Segment Name');
    });
  });
});
