import { TestBed } from '@angular/core/testing';
import { RouteConfigService } from './route-config.service';

describe('RouteConfigService', () => {
  let service: RouteConfigService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(RouteConfigService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Route Building', () => {
    it('should build home route correctly', () => {
      const homeRoute = service.getHomeRoute();
      expect(homeRoute).toBe('/experience/main');
    });

    it('should build project route without action', () => {
      const projectRoute = service.getProjectRoute('test-project-id');
      expect(projectRoute).toBe('/experience/projects/test-project-id');
    });

    it('should build project route with edit action', () => {
      const projectRoute = service.getProjectRoute('test-project-id', 'edit');
      expect(projectRoute).toBe('/experience/projects/test-project-id/edit');
    });

    it('should build project route with share action', () => {
      const projectRoute = service.getProjectRoute('test-project-id', 'share');
      expect(projectRoute).toBe('/experience/projects/test-project-id/share');
    });

    it('should build UI design new route', () => {
      const uiDesignRoute = service.getUIDesignRoute();
      expect(uiDesignRoute).toBe('/experience/ui-design/new');
    });

    it('should build UI design project route', () => {
      const uiDesignRoute = service.getUIDesignRoute('test-project-id');
      expect(uiDesignRoute).toBe('/experience/ui-design/test-project-id');
    });

    it('should build UI design project route with preview action', () => {
      const uiDesignRoute = service.getUIDesignRoute('test-project-id', 'preview');
      expect(uiDesignRoute).toBe('/experience/ui-design/test-project-id/preview');
    });

    it('should build application new route', () => {
      const appRoute = service.getApplicationRoute();
      expect(appRoute).toBe('/experience/application/new');
    });

    it('should build application project route with edit action', () => {
      const appRoute = service.getApplicationRoute('test-project-id', 'edit');
      expect(appRoute).toBe('/experience/application/test-project-id/edit');
    });
  });

  describe('Route Validation', () => {
    it('should identify project routes correctly', () => {
      expect(service.isProjectRoute('/experience/projects/test-id')).toBe(true);
      expect(service.isProjectRoute('/experience/ui-design/test-id')).toBe(true);
      expect(service.isProjectRoute('/experience/application/test-id')).toBe(true);
      expect(service.isProjectRoute('/experience/main')).toBe(false);
    });

    it('should extract project ID from routes', () => {
      expect(service.extractProjectId('/experience/projects/test-id')).toBe('test-id');
      expect(service.extractProjectId('/experience/ui-design/test-id')).toBe('test-id');
      expect(service.extractProjectId('/experience/application/test-id/edit')).toBe('test-id');
      expect(service.extractProjectId('/experience/ui-design/new')).toBeNull();
      expect(service.extractProjectId('/experience/main')).toBeNull();
    });

    it('should extract project type from routes', () => {
      expect(service.extractProjectType('/experience/ui-design/test-id')).toBe('ui-design');
      expect(service.extractProjectType('/experience/application/test-id')).toBe('application');
      expect(service.extractProjectType('/experience/projects/test-id')).toBe('generic');
      expect(service.extractProjectType('/experience/main')).toBeNull();
    });

    it('should extract action from routes', () => {
      expect(service.extractAction('/experience/ui-design/new')).toBe('new');
      expect(service.extractAction('/experience/projects/test-id/edit')).toBe('edit');
      expect(service.extractAction('/experience/application/test-id/preview')).toBe('preview');
      expect(service.extractAction('/experience/projects/test-id/share')).toBe('share');
      expect(service.extractAction('/experience/ui-design/test-id')).toBe('view');
      expect(service.extractAction('/experience/main')).toBeNull();
    });
  });

  describe('Legacy Route Mapping', () => {
    it('should map legacy prompt route to new application route', () => {
      const mapped = service.mapLegacyRoute('/experience/prompt');
      expect(mapped).toBe('/experience/application/new');
    });

    it('should map legacy generate-application prompt route', () => {
      const mapped = service.mapLegacyRoute('/experience/generate-application/prompt');
      expect(mapped).toBe('/experience/application/new');
    });

    it('should map legacy generate-ui-design prompt route', () => {
      const mapped = service.mapLegacyRoute('/experience/generate-ui-design/prompt');
      expect(mapped).toBe('/experience/ui-design/new');
    });

    it('should map legacy project-specific route', () => {
      const legacyRoute = '/experience/generate-application/code-preview/projects/test-id';
      const mapped = service.mapLegacyRoute(legacyRoute);
      expect(mapped).toBe('/experience/projects/test-id');
    });

    it('should return unchanged route if no mapping exists', () => {
      const unmappedRoute = '/experience/some-unknown-route';
      const mapped = service.mapLegacyRoute(unmappedRoute);
      expect(mapped).toBe(unmappedRoute);
    });
  });

  describe('Route Metadata', () => {
    it('should generate metadata for UI design route', () => {
      const metadata = service.getRouteMetadata('/experience/ui-design/test-id');
      expect(metadata.type).toBe('ui-design');
      expect(metadata.projectId).toBe('test-id');
      expect(metadata.action).toBe('view');
      expect(metadata.category).toBe('project');
    });

    it('should generate metadata for new application route', () => {
      const metadata = service.getRouteMetadata('/experience/application/new');
      expect(metadata.type).toBe('application');
      expect(metadata.projectId).toBeUndefined();
      expect(metadata.action).toBe('new');
      expect(metadata.category).toBe('navigation');
    });

    it('should generate metadata for main route', () => {
      const metadata = service.getRouteMetadata('/experience/main');
      expect(metadata.type).toBe('unknown');
      expect(metadata.category).toBe('navigation');
    });
  });

  describe('URL Generation', () => {
    beforeEach(() => {
      // Mock window.location.origin
      Object.defineProperty(window, 'location', {
        value: { origin: 'https://example.com' },
        writable: true
      });
    });

    it('should generate shareable URL for UI design project', () => {
      const url = service.generateShareableUrl('test-id', 'ui-design');
      expect(url).toBe('https://example.com/experience/ui-design/test-id');
    });

    it('should generate shareable URL for generic project', () => {
      const url = service.generateShareableUrl('test-id');
      expect(url).toBe('https://example.com/experience/projects/test-id');
    });

    it('should generate deep link with query parameters', () => {
      const url = service.generateDeepLink('application', 'test-id', 'edit', { 
        theme: 'dark', 
        tab: 'code' 
      });
      expect(url).toBe('/experience/application/test-id/edit?theme=dark&tab=code');
    });

    it('should generate deep link without query parameters', () => {
      const url = service.generateDeepLink('ui-design', 'test-id', 'preview');
      expect(url).toBe('/experience/ui-design/test-id/preview');
    });

    it('should generate short URL', () => {
      const shortUrl = service.generateShortUrl('12345678-1234-1234-1234-123456789012', 'ui-design');
      expect(shortUrl).toBe('https://example.com/p/u/12345678');
    });

    it('should generate embed URL with options', () => {
      const embedUrl = service.generateEmbedUrl('test-id', {
        theme: 'dark',
        hideNavigation: true,
        autoplay: true
      });
      expect(embedUrl).toBe('https://example.com/experience/projects/test-id/preview?embed=true&theme=dark&hideNav=true&autoplay=true');
    });

    it('should generate API URL with project ID', () => {
      const apiUrl = service.generateApiUrl('projects', 'test-id');
      expect(apiUrl).toBe('/api/v1/projects/test-id');
    });

    it('should generate API URL without project ID', () => {
      const apiUrl = service.generateApiUrl('health');
      expect(apiUrl).toBe('/api/v1/health');
    });

    it('should generate webhook URL', () => {
      const webhookUrl = service.generateWebhookUrl('test-id', 'completed');
      expect(webhookUrl).toBe('https://example.com/webhooks/projects/test-id/events/completed');
    });

    it('should generate download URL', () => {
      const downloadUrl = service.generateDownloadUrl('test-id', 'code');
      expect(downloadUrl).toBe('/api/v1/projects/test-id/download/code');
    });

    it('should generate social sharing URLs', () => {
      const socialUrls = service.generateSocialShareUrls('test-id', 'My Project');
      
      expect(socialUrls.twitter).toContain('twitter.com/intent/tweet');
      expect(socialUrls.linkedin).toContain('linkedin.com/sharing');
      expect(socialUrls.facebook).toContain('facebook.com/sharer');
      expect(socialUrls.email).toContain('mailto:');
      
      // Check that URLs are properly encoded
      expect(socialUrls.twitter).toContain(encodeURIComponent('Check out my My Project project'));
    });
  });

  describe('URL Validation', () => {
    it('should validate valid HTTPS URL', () => {
      const result = service.validateUrl('https://example.com/path');
      expect(result.isValid).toBe(true);
      expect(result.issues).toEqual([]);
    });

    it('should validate valid HTTP URL', () => {
      const result = service.validateUrl('http://localhost:3000/path');
      expect(result.isValid).toBe(true);
      expect(result.issues).toEqual([]);
    });

    it('should reject invalid protocol', () => {
      const result = service.validateUrl('ftp://example.com/path');
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Invalid protocol - only HTTP and HTTPS are allowed');
    });

    it('should reject malicious JavaScript URL', () => {
      const result = service.validateUrl('javascript:alert("xss")');
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('URL contains potentially malicious content');
    });

    it('should reject data URL', () => {
      const result = service.validateUrl('data:text/html,<script>alert("xss")</script>');
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('URL contains potentially malicious content');
    });

    it('should reject URL with script tag', () => {
      const result = service.validateUrl('https://example.com/<script>alert("xss")</script>');
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('URL contains potentially malicious content');
    });

    it('should reject overly long URL', () => {
      const longUrl = 'https://example.com/' + 'a'.repeat(2050);
      const result = service.validateUrl(longUrl);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('URL is too long (max 2048 characters)');
    });

    it('should reject malformed URL', () => {
      const result = service.validateUrl('not-a-url');
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Invalid URL format');
    });
  });

  describe('Collaboration Features', () => {
    it('should generate collaboration URL', () => {
      const collabUrl = service.generateCollaborationUrl('test-id', 'edit');
      expect(collabUrl).toContain('https://example.com/experience/projects/test-id');
      expect(collabUrl).toContain('collab=');
    });

    it('should parse collaboration token', () => {
      const token = btoa(JSON.stringify({ 
        projectId: 'test-id', 
        permissions: 'edit', 
        timestamp: 1234567890 
      }));
      
      const parsed = service.parseCollaborationToken(token);
      expect(parsed).toEqual({
        projectId: 'test-id',
        permissions: 'edit',
        timestamp: 1234567890
      });
    });

    it('should return null for invalid collaboration token', () => {
      const parsed = service.parseCollaborationToken('invalid-token');
      expect(parsed).toBeNull();
    });
  });
});
