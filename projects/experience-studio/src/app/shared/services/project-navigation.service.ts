import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { ToastService } from './toast.service';

/**
 * ENHANCED: Service to handle project navigation with enterprise routing patterns
 * Provides centralized navigation logic for recent projects with new URL structure
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectNavigationService {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);

  /**
   * ENHANCED: Navigate to project with new enterprise route structure
   * @param projectId The project ID to navigate to
   * @param projectName Optional project name for toast message
   * @param projectType Optional project type ('ui-design' | 'application') for specific routing
   * @param mode Optional mode ('view' | 'edit' | 'preview') for specific actions
   */
  navigateToProject(
    projectId: string,
    projectName?: string,
    projectType?: 'ui-design' | 'application',
    mode: 'view' | 'edit' | 'preview' = 'view'
  ): void {
    if (!projectId) {
      this.toastService.error('Invalid project ID');
      return;
    }

    // Show loading toast
    if (projectName) {
      this.toastService.info(`Loading project "${projectName}"...`);
    } else {
      this.toastService.info('Loading project...');
    }

    // ENHANCED: Build route based on project type and mode
    let targetRoute: string[];

    if (projectType) {
      // Type-specific routing
      const basePath = `/experience/${projectType}/${projectId}`;
      targetRoute = mode === 'view' ? [basePath] : [`${basePath}/${mode}`];
    } else {
      // Generic project routing (fallback)
      const basePath = `/experience/projects/${projectId}`;
      targetRoute = mode === 'view' ? [basePath] : [`${basePath}/${mode}`];
    }

    // Navigate to the correct route with project loading enabled
    this.router.navigate(targetRoute)
      .then(success => {
        if (!success) {
          this.toastService.error('Failed to navigate to project');
        }
      })
      .catch(error => {
        console.error('Navigation error:', error);
        this.toastService.error('Failed to navigate to project');
      });
  }

  /**
   * ENHANCED: Navigate to create new project of specific type
   * @param projectType The type of project to create ('ui-design' | 'application')
   */
  navigateToNewProject(projectType: 'ui-design' | 'application'): void {
    const targetRoute = [`/experience/${projectType}/new`];

    this.toastService.info(`Starting new ${projectType.replace('-', ' ')} project...`);

    this.router.navigate(targetRoute)
      .then(success => {
        if (!success) {
          this.toastService.error('Failed to navigate to new project');
        }
      })
      .catch(error => {
        console.error('Navigation error:', error);
        this.toastService.error('Failed to navigate to new project');
      });
  }

  /**
   * ENHANCED: Check if the current route is a project route (any type)
   * @returns boolean indicating if currently on project route
   */
  isOnProjectRoute(): boolean {
    const url = this.router.url;
    return url.includes('/projects/') ||
           url.includes('/ui-design/') ||
           url.includes('/application/');
  }

  /**
   * ENHANCED: Extract project ID from current route with new URL patterns
   * @returns project ID if on project route, null otherwise
   */
  getCurrentProjectId(): string | null {
    const url = this.router.url;

    // Match various project route patterns
    const patterns = [
      /\/projects\/([^\/]+)/,           // /experience/projects/{id}
      /\/ui-design\/([^\/]+)/,          // /experience/ui-design/{id}
      /\/application\/([^\/]+)/,        // /experience/application/{id}
      /\/code-preview\/projects\/([^\/]+)/ // Legacy pattern
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1] !== 'new') { // Exclude 'new' route
        return match[1];
      }
    }

    return null;
  }

  /**
   * ENHANCED: Get current project type from route
   * @returns project type if determinable, null otherwise
   */
  getCurrentProjectType(): 'ui-design' | 'application' | null {
    const url = this.router.url;

    if (url.includes('/ui-design/')) {
      return 'ui-design';
    } else if (url.includes('/application/')) {
      return 'application';
    }

    return null;
  }

  /**
   * ENHANCED: Get current route mode
   * @returns current mode ('view' | 'edit' | 'preview' | 'new')
   */
  getCurrentMode(): 'view' | 'edit' | 'preview' | 'new' | null {
    const url = this.router.url;

    if (url.includes('/new')) {
      return 'new';
    } else if (url.includes('/edit')) {
      return 'edit';
    } else if (url.includes('/preview')) {
      return 'preview';
    } else if (this.getCurrentProjectId()) {
      return 'view';
    }

    return null;
  }

  /**
   * ENHANCED: Generate breadcrumb data for current route
   * @returns breadcrumb array for navigation
   */
  getBreadcrumbs(): Array<{label: string, route?: string}> {
    const breadcrumbs = [{label: 'Home', route: '/experience/main'}];

    const projectType = this.getCurrentProjectType();
    const projectId = this.getCurrentProjectId();
    const mode = this.getCurrentMode();

    if (projectType) {
      const typeLabel = projectType === 'ui-design' ? 'UI Design' : 'Application';
      breadcrumbs.push({
        label: typeLabel,
        route: `/experience/${projectType}/new`
      });

      if (projectId) {
        breadcrumbs.push({
          label: 'Project',
          route: `/experience/${projectType}/${projectId}`
        });

        if (mode && mode !== 'view') {
          breadcrumbs.push({
            label: mode.charAt(0).toUpperCase() + mode.slice(1)
          });
        }
      } else if (mode === 'new') {
        breadcrumbs.push({label: 'New'});
      }
    } else if (projectId) {
      breadcrumbs.push({
        label: 'Project',
        route: `/experience/projects/${projectId}`
      });
    }

    return breadcrumbs;
  }
}
