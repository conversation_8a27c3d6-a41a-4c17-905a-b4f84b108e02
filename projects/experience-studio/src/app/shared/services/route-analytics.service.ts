import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouteStateService } from './route-state.service';
import { RouteConfigService } from './route-config.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Route analytics service for tracking user navigation patterns
 * Provides insights into user behavior and route performance
 */
export interface RouteAnalyticsEvent {
  timestamp: number;
  route: string;
  routeType: string;
  projectId?: string;
  projectType?: string;
  action?: string;
  duration?: number;
  referrer?: string;
  userAgent?: string;
  sessionId: string;
}

export interface RouteMetrics {
  totalPageViews: number;
  uniqueRoutes: number;
  averageSessionDuration: number;
  mostVisitedRoutes: Array<{ route: string; count: number }>;
  projectTypeDistribution: Record<string, number>;
  actionDistribution: Record<string, number>;
  bounceRate: number;
  conversionFunnel: {
    landingPage: number;
    projectCreation: number;
    projectCompletion: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class RouteAnalyticsService {
  private readonly router = inject(Router);
  private readonly routeStateService = inject(RouteStateService);
  private readonly routeConfigService = inject(RouteConfigService);
  private readonly logger = createLogger('RouteAnalyticsService');

  // ENHANCED: Analytics state with Angular 19+ Signals
  private readonly events = signal<RouteAnalyticsEvent[]>([]);
  private readonly sessionId = signal<string>(this.generateSessionId());
  private readonly sessionStartTime = signal<number>(Date.now());
  private readonly currentRouteStartTime = signal<number>(Date.now());

  // ENHANCED: Computed analytics metrics
  readonly metrics = computed<RouteMetrics>(() => this.calculateMetrics());
  readonly currentSessionDuration = computed(() => 
    Date.now() - this.sessionStartTime()
  );
  readonly recentEvents = computed(() => 
    this.events().slice(-10)
  );

  // ENHANCED: Real-time analytics
  private readonly isTrackingEnabled = signal<boolean>(true);
  private readonly maxEvents = signal<number>(1000);

  constructor() {
    this.initializeTracking();
    this.setupAnalyticsEffects();
  }

  /**
   * Initialize route tracking
   */
  private initializeTracking(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(event => event as NavigationEnd),
      takeUntilDestroyed()
    ).subscribe(event => {
      this.trackRouteNavigation(event);
    });
  }

  /**
   * Setup reactive effects for analytics
   */
  private setupAnalyticsEffects(): void {
    // Log analytics events in development
    effect(() => {
      const events = this.events();
      if (events.length > 0) {
        const latestEvent = events[events.length - 1];
        this.logger.debug('Analytics event tracked:', latestEvent);
      }
    });

    // Persist analytics data periodically
    effect(() => {
      const events = this.events();
      if (events.length > 0 && events.length % 10 === 0) {
        this.persistAnalyticsData();
      }
    });

    // Clean up old events
    effect(() => {
      const events = this.events();
      const maxEvents = this.maxEvents();
      
      if (events.length > maxEvents) {
        const trimmedEvents = events.slice(-maxEvents);
        this.events.set(trimmedEvents);
      }
    });
  }

  /**
   * Track route navigation event
   */
  private trackRouteNavigation(navigationEvent: NavigationEnd): void {
    if (!this.isTrackingEnabled()) {
      return;
    }

    const now = Date.now();
    const routeState = this.routeStateService.getCurrentState();
    const previousDuration = now - this.currentRouteStartTime();

    // Update the previous event with duration if it exists
    const currentEvents = this.events();
    if (currentEvents.length > 0) {
      const lastEvent = currentEvents[currentEvents.length - 1];
      lastEvent.duration = previousDuration;
    }

    // Create new analytics event
    const analyticsEvent: RouteAnalyticsEvent = {
      timestamp: now,
      route: navigationEvent.urlAfterRedirects,
      routeType: routeState.routeType,
      projectId: routeState.projectId,
      projectType: routeState.projectType,
      action: routeState.action,
      referrer: this.getPreviousRoute(),
      userAgent: navigator.userAgent,
      sessionId: this.sessionId()
    };

    // Add event to collection
    this.events.update(events => [...events, analyticsEvent]);
    this.currentRouteStartTime.set(now);

    // Send to external analytics if configured
    this.sendToExternalAnalytics(analyticsEvent);
  }

  /**
   * Calculate comprehensive metrics from events
   */
  private calculateMetrics(): RouteMetrics {
    const events = this.events();
    
    if (events.length === 0) {
      return this.getEmptyMetrics();
    }

    const totalPageViews = events.length;
    const uniqueRoutes = new Set(events.map(e => e.route)).size;
    
    // Calculate average session duration
    const sessionsMap = new Map<string, RouteAnalyticsEvent[]>();
    events.forEach(event => {
      if (!sessionsMap.has(event.sessionId)) {
        sessionsMap.set(event.sessionId, []);
      }
      sessionsMap.get(event.sessionId)!.push(event);
    });

    const sessionDurations = Array.from(sessionsMap.values()).map(sessionEvents => {
      if (sessionEvents.length < 2) return 0;
      const first = sessionEvents[0];
      const last = sessionEvents[sessionEvents.length - 1];
      return last.timestamp - first.timestamp;
    });

    const averageSessionDuration = sessionDurations.length > 0 
      ? sessionDurations.reduce((a, b) => a + b, 0) / sessionDurations.length 
      : 0;

    // Most visited routes
    const routeCounts = new Map<string, number>();
    events.forEach(event => {
      routeCounts.set(event.route, (routeCounts.get(event.route) || 0) + 1);
    });

    const mostVisitedRoutes = Array.from(routeCounts.entries())
      .map(([route, count]) => ({ route, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Project type distribution
    const projectTypeDistribution: Record<string, number> = {};
    events.forEach(event => {
      if (event.projectType) {
        projectTypeDistribution[event.projectType] = 
          (projectTypeDistribution[event.projectType] || 0) + 1;
      }
    });

    // Action distribution
    const actionDistribution: Record<string, number> = {};
    events.forEach(event => {
      if (event.action) {
        actionDistribution[event.action] = 
          (actionDistribution[event.action] || 0) + 1;
      }
    });

    // Bounce rate (sessions with only one page view)
    const singlePageSessions = Array.from(sessionsMap.values())
      .filter(sessionEvents => sessionEvents.length === 1).length;
    const bounceRate = sessionsMap.size > 0 ? singlePageSessions / sessionsMap.size : 0;

    // Conversion funnel
    const landingPageViews = events.filter(e => e.route.includes('/main')).length;
    const projectCreationViews = events.filter(e => e.action === 'new').length;
    const projectCompletionViews = events.filter(e => 
      e.action === 'view' && e.projectId
    ).length;

    return {
      totalPageViews,
      uniqueRoutes,
      averageSessionDuration,
      mostVisitedRoutes,
      projectTypeDistribution,
      actionDistribution,
      bounceRate,
      conversionFunnel: {
        landingPage: landingPageViews,
        projectCreation: projectCreationViews,
        projectCompletion: projectCompletionViews
      }
    };
  }

  /**
   * Get empty metrics structure
   */
  private getEmptyMetrics(): RouteMetrics {
    return {
      totalPageViews: 0,
      uniqueRoutes: 0,
      averageSessionDuration: 0,
      mostVisitedRoutes: [],
      projectTypeDistribution: {},
      actionDistribution: {},
      bounceRate: 0,
      conversionFunnel: {
        landingPage: 0,
        projectCreation: 0,
        projectCompletion: 0
      }
    };
  }

  /**
   * Get previous route from events
   */
  private getPreviousRoute(): string | undefined {
    const events = this.events();
    return events.length > 0 ? events[events.length - 1].route : undefined;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Persist analytics data to storage
   */
  private persistAnalyticsData(): void {
    try {
      const data = {
        events: this.events(),
        sessionId: this.sessionId(),
        sessionStartTime: this.sessionStartTime(),
        timestamp: Date.now()
      };
      
      localStorage.setItem('route-analytics', JSON.stringify(data));
      this.logger.debug('Analytics data persisted to localStorage');
    } catch (error) {
      this.logger.warn('Failed to persist analytics data:', error);
    }
  }

  /**
   * Load persisted analytics data
   */
  private loadPersistedData(): void {
    try {
      const stored = localStorage.getItem('route-analytics');
      if (stored) {
        const data = JSON.parse(stored);
        
        // Only load if from same session or recent
        const isRecentSession = Date.now() - data.timestamp < 24 * 60 * 60 * 1000; // 24 hours
        
        if (isRecentSession && data.events) {
          this.events.set(data.events);
          this.logger.debug('Analytics data loaded from localStorage');
        }
      }
    } catch (error) {
      this.logger.warn('Failed to load persisted analytics data:', error);
    }
  }

  /**
   * Send analytics to external service (placeholder)
   */
  private sendToExternalAnalytics(event: RouteAnalyticsEvent): void {
    // TODO: Implement actual external analytics integration
    // Examples: Google Analytics, Adobe Analytics, Mixpanel, etc.
    
    this.logger.debug('Would send to external analytics:', {
      event_name: 'page_view',
      page_path: event.route,
      page_title: event.routeType,
      custom_parameters: {
        project_type: event.projectType,
        action: event.action,
        session_id: event.sessionId
      }
    });
  }

  // ===== PUBLIC API =====

  /**
   * Get current analytics metrics
   */
  getMetrics(): RouteMetrics {
    return this.metrics();
  }

  /**
   * Track custom event
   */
  trackCustomEvent(eventName: string, properties: Record<string, any>): void {
    if (!this.isTrackingEnabled()) {
      return;
    }

    const customEvent: RouteAnalyticsEvent = {
      timestamp: Date.now(),
      route: this.router.url,
      routeType: `custom:${eventName}`,
      sessionId: this.sessionId(),
      ...properties
    };

    this.events.update(events => [...events, customEvent]);
    this.logger.info('Custom event tracked:', eventName, properties);
  }

  /**
   * Enable/disable tracking
   */
  setTrackingEnabled(enabled: boolean): void {
    this.isTrackingEnabled.set(enabled);
    this.logger.info('Analytics tracking', enabled ? 'enabled' : 'disabled');
  }

  /**
   * Clear analytics data
   */
  clearAnalyticsData(): void {
    this.events.set([]);
    localStorage.removeItem('route-analytics');
    this.logger.info('Analytics data cleared');
  }

  /**
   * Export analytics data
   */
  exportAnalyticsData(): string {
    const data = {
      events: this.events(),
      metrics: this.metrics(),
      sessionInfo: {
        sessionId: this.sessionId(),
        sessionStartTime: this.sessionStartTime(),
        sessionDuration: this.currentSessionDuration()
      },
      exportTimestamp: Date.now()
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * Get route performance insights
   */
  getRoutePerformanceInsights(): Array<{
    route: string;
    averageDuration: number;
    visitCount: number;
    bounceRate: number;
  }> {
    const events = this.events();
    const routeStats = new Map<string, {
      durations: number[];
      visits: number;
      bounces: number;
    }>();

    events.forEach(event => {
      if (!routeStats.has(event.route)) {
        routeStats.set(event.route, { durations: [], visits: 0, bounces: 0 });
      }
      
      const stats = routeStats.get(event.route)!;
      stats.visits++;
      
      if (event.duration) {
        stats.durations.push(event.duration);
      }
      
      // Simple bounce detection (duration < 5 seconds)
      if (event.duration && event.duration < 5000) {
        stats.bounces++;
      }
    });

    return Array.from(routeStats.entries()).map(([route, stats]) => ({
      route,
      averageDuration: stats.durations.length > 0 
        ? stats.durations.reduce((a, b) => a + b, 0) / stats.durations.length 
        : 0,
      visitCount: stats.visits,
      bounceRate: stats.visits > 0 ? stats.bounces / stats.visits : 0
    }));
  }
}
