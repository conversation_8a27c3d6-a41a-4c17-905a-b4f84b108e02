import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRouteSnapshot } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ProjectAccessGuard, ProjectOwnershipGuard, RouteValidationGuard } from './project-access.guard';
import { RecentProjectService } from '../services/recent-project-services/recent-project.service';
import { ToastService } from '../services/toast.service';
import { UuidService } from '../services/uuid.service';

describe('ProjectAccessGuard', () => {
  let guard: ProjectAccessGuard;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRecentProjectService: jasmine.SpyObj<RecentProjectService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockUuidService: jasmine.SpyObj<UuidService>;
  let mockRoute: ActivatedRouteSnapshot;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['createUrlTree']);
    const recentProjectSpy = jasmine.createSpyObj('RecentProjectService', ['getProjectById']);
    const toastSpy = jasmine.createSpyObj('ToastService', ['error']);
    const uuidSpy = jasmine.createSpyObj('UuidService', ['isValidProjectId']);

    TestBed.configureTestingModule({
      providers: [
        ProjectAccessGuard,
        { provide: Router, useValue: routerSpy },
        { provide: RecentProjectService, useValue: recentProjectSpy },
        { provide: ToastService, useValue: toastSpy },
        { provide: UuidService, useValue: uuidSpy }
      ]
    });

    guard = TestBed.inject(ProjectAccessGuard);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRecentProjectService = TestBed.inject(RecentProjectService) as jasmine.SpyObj<RecentProjectService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockUuidService = TestBed.inject(UuidService) as jasmine.SpyObj<UuidService>;

    // Setup mock route
    mockRoute = {
      paramMap: new Map([['projectId', 'test-project-id']]),
      data: { routeType: 'project-view' }
    } as any;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow access when skipGuards is true', (done) => {
    mockRoute.data = { skipGuards: true };

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should deny access for invalid project ID', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(false);
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Invalid project ID');
      expect(mockRouter.createUrlTree).toHaveBeenCalledWith(['/experience/main']);
      done();
    });
  });

  it('should deny access when no project ID is provided', (done) => {
    mockRoute.paramMap = new Map();
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Invalid project ID');
      done();
    });
  });

  it('should allow access when project exists and user has access', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRecentProjectService.getProjectById.and.returnValue(of({
      project: { project_id: 'test-project-id', project_name: 'Test Project' }
    } as any));

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should deny access when project does not exist', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRecentProjectService.getProjectById.and.returnValue(of({ project: null } as any));
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('You do not have permission to access this project');
      done();
    });
  });

  it('should handle service errors gracefully', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRecentProjectService.getProjectById.and.returnValue(throwError('Service error'));
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Failed to verify project access');
      done();
    });
  });
});

describe('ProjectOwnershipGuard', () => {
  let guard: ProjectOwnershipGuard;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRecentProjectService: jasmine.SpyObj<RecentProjectService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockUuidService: jasmine.SpyObj<UuidService>;
  let mockRoute: ActivatedRouteSnapshot;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['createUrlTree']);
    const recentProjectSpy = jasmine.createSpyObj('RecentProjectService', ['getProjectById']);
    const toastSpy = jasmine.createSpyObj('ToastService', ['error']);
    const uuidSpy = jasmine.createSpyObj('UuidService', ['isValidProjectId']);

    TestBed.configureTestingModule({
      providers: [
        ProjectOwnershipGuard,
        { provide: Router, useValue: routerSpy },
        { provide: RecentProjectService, useValue: recentProjectSpy },
        { provide: ToastService, useValue: toastSpy },
        { provide: UuidService, useValue: uuidSpy }
      ]
    });

    guard = TestBed.inject(ProjectOwnershipGuard);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRecentProjectService = TestBed.inject(RecentProjectService) as jasmine.SpyObj<RecentProjectService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockUuidService = TestBed.inject(UuidService) as jasmine.SpyObj<UuidService>;

    mockRoute = {
      paramMap: new Map([['projectId', 'test-project-id']]),
      data: { routeType: 'project-edit' },
      routeConfig: { path: 'projects/:projectId/edit' }
    } as any;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow access for non-restricted actions', (done) => {
    mockRoute.data = { routeType: 'project-view' };

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should check ownership for edit actions', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRecentProjectService.getProjectById.and.returnValue(of({
      project: { project_id: 'test-project-id', project_name: 'Test Project' }
    } as any));

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      expect(mockRecentProjectService.getProjectById).toHaveBeenCalledWith('test-project-id');
      done();
    });
  });

  it('should deny access for invalid project ID on edit', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(false);
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Invalid project ID');
      done();
    });
  });

  it('should redirect to view mode when ownership is denied', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRecentProjectService.getProjectById.and.returnValue(of({ project: null } as any));
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('You do not have permission to modify this project');
      done();
    });
  });
});

describe('RouteValidationGuard', () => {
  let guard: RouteValidationGuard;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockUuidService: jasmine.SpyObj<UuidService>;
  let mockRoute: ActivatedRouteSnapshot;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['createUrlTree']);
    const toastSpy = jasmine.createSpyObj('ToastService', ['error']);
    const uuidSpy = jasmine.createSpyObj('UuidService', ['isValidProjectId']);

    TestBed.configureTestingModule({
      providers: [
        RouteValidationGuard,
        { provide: Router, useValue: routerSpy },
        { provide: ToastService, useValue: toastSpy },
        { provide: UuidService, useValue: uuidSpy }
      ]
    });

    guard = TestBed.inject(RouteValidationGuard);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockUuidService = TestBed.inject(UuidService) as jasmine.SpyObj<UuidService>;

    mockRoute = {
      paramMap: new Map([['projectId', 'test-project-id']]),
      routeConfig: { path: 'projects/:projectId' },
      url: [{ path: 'projects' }, { path: 'test-project-id' }],
      queryParams: {}
    } as any;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow valid routes', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(true);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      done();
    });
  });

  it('should reject invalid project ID format', (done) => {
    mockUuidService.isValidProjectId.and.returnValue(false);
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Invalid project URL');
      done();
    });
  });

  it('should reject invalid route structure', (done) => {
    mockRoute.routeConfig = { path: 'invalid/route/structure' };
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockToastService.error).toHaveBeenCalledWith('Invalid URL structure');
      done();
    });
  });

  it('should detect and clean suspicious query parameters', (done) => {
    mockRoute.queryParams = { 
      normal: 'value',
      malicious: '<script>alert("xss")</script>'
    };
    mockUuidService.isValidProjectId.and.returnValue(true);
    mockRouter.createUrlTree.and.returnValue({} as any);

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).not.toBe(true);
      expect(mockRouter.createUrlTree).toHaveBeenCalledWith(['projects/test-project-id']);
      done();
    });
  });

  it('should allow routes without project ID', (done) => {
    mockRoute.paramMap = new Map();
    mockRoute.routeConfig = { path: 'main' };

    guard.canActivate(mockRoute).subscribe(result => {
      expect(result).toBe(true);
      done();
    });
  });
});
