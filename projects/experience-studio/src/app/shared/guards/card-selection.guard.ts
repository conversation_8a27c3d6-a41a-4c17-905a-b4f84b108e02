import { Injectable, inject } from '@angular/core';
import { CanActivate, UrlTree, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { CardSelectionService } from '../services/card-selection.service';
import { ToastService } from '../services/toast.service';
import { createLogger } from '../utils';

/**
 * ENHANCED: Card selection guard with improved enterprise routing support
 * Ensures users have selected a card before accessing creation flows
 */
@Injectable({
  providedIn: 'root'
})
export class CardSelectionGuard implements CanActivate {
  private readonly cardSelectionService = inject(CardSelectionService);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly logger = createLogger('CardSelectionGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const routeType = route.data?.['routeType'] || 'unknown';
    const cardType = route.data?.['cardType'] || 'Unknown';

    this.logger.info('Checking card selection for route:', { routeType, cardType });

    // Skip guard for certain routes
    if (this.shouldSkipCardSelection(route)) {
      this.logger.info('Skipping card selection check');
      return true;
    }

    // Check if the user has selected a card
    if (this.cardSelectionService.hasCardBeenSelected()) {
      this.logger.info('Card selection validated');
      return true;
    }

    // For new project routes, we can auto-select the appropriate card
    if (this.isNewProjectRoute(routeType)) {
      this.logger.info('Auto-selecting card for new project route:', cardType);
      this.cardSelectionService.setCardSelected(true);
      // Set the card title based on route type
      this.setCardTitleFromRoute(routeType);
      return true;
    }

    // If not selected and not a new project route, redirect back to the landing page
    this.logger.warn('No card selected, redirecting to main page');
    this.toastService.info('Please select a project type to continue');
    return this.router.createUrlTree(['/experience/main']);
  }

  /**
   * Check if card selection should be skipped for this route
   */
  private shouldSkipCardSelection(route: ActivatedRouteSnapshot): boolean {
    return route.data?.['skipGuards'] ||
           route.data?.['skipCardSelection'] ||
           route.data?.['isProjectLoading'];
  }

  /**
   * Check if this is a new project creation route
   */
  private isNewProjectRoute(routeType: string): boolean {
    return routeType === 'ui-design-new' || routeType === 'application-new';
  }

  /**
   * Set card title based on route type
   */
  private setCardTitleFromRoute(routeType: string): void {
    // TODO: Implement card title setting when CardDataService is available
    // For now, just log the action
    this.logger.info('Setting card title for route type:', routeType);

    if (routeType === 'ui-design-new') {
      this.logger.info('Would set card title to: Generate UI Design');
    } else if (routeType === 'application-new') {
      this.logger.info('Would set card title to: Generate Application');
    }
  }
}
