import { Component, ChangeDetectionStrategy, inject, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { BreadcrumbService, BreadcrumbItem } from '../../services/breadcrumb.service';
import { RouteStateService } from '../../services/route-state.service';

/**
 * ENTERPRISE: Breadcrumb navigation component with Angular 19+ features
 * Provides dynamic breadcrumb navigation based on current route
 */
@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [CommonModule, RouterModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <nav class="breadcrumb-nav" [attr.aria-label]="ariaLabel()" role="navigation">
      <ol class="breadcrumb-list">
        @for (breadcrumb of breadcrumbs(); track breadcrumb.label; let isLast = $last) {
          <li 
            class="breadcrumb-item"
            [class.breadcrumb-item--active]="breadcrumb.isActive || isLast"
            [class.breadcrumb-item--clickable]="!!breadcrumb.route && !breadcrumb.isActive">
            
            @if (breadcrumb.route && !breadcrumb.isActive) {
              <a 
                [routerLink]="breadcrumb.route"
                class="breadcrumb-link"
                [attr.aria-current]="breadcrumb.isActive ? 'page' : null"
                (click)="onBreadcrumbClick(breadcrumb)">
                
                @if (breadcrumb.icon) {
                  <i class="breadcrumb-icon" [class]="'icon-' + breadcrumb.icon" aria-hidden="true"></i>
                }
                
                <span class="breadcrumb-text">{{ breadcrumb.label }}</span>
              </a>
            } @else {
              <span 
                class="breadcrumb-text breadcrumb-text--current"
                [attr.aria-current]="breadcrumb.isActive ? 'page' : null">
                
                @if (breadcrumb.icon) {
                  <i class="breadcrumb-icon" [class]="'icon-' + breadcrumb.icon" aria-hidden="true"></i>
                }
                
                {{ breadcrumb.label }}
              </span>
            }
            
            @if (!isLast) {
              <span class="breadcrumb-separator" aria-hidden="true">
                {{ separator() }}
              </span>
            }
          </li>
        }
      </ol>
      
      @if (showBackButton() && canGoBack()) {
        <button 
          type="button"
          class="breadcrumb-back-btn"
          (click)="goBack()"
          [attr.aria-label]="'Go back to ' + getPreviousBreadcrumbLabel()">
          <i class="icon-arrow-left" aria-hidden="true"></i>
          <span class="sr-only">Back</span>
        </button>
      }
    </nav>
  `,
  styles: [`
    .breadcrumb-nav {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      font-size: 0.875rem;
      color: var(--text-secondary, #6b7280);
    }

    .breadcrumb-list {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0;
      padding: 0;
      list-style: none;
      flex-wrap: wrap;
    }

    .breadcrumb-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .breadcrumb-link {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      color: var(--text-secondary, #6b7280);
      text-decoration: none;
      transition: all 0.15s ease;
    }

    .breadcrumb-link:hover {
      color: var(--primary-color, #3b82f6);
      background-color: var(--bg-hover, #f3f4f6);
    }

    .breadcrumb-link:focus {
      outline: 2px solid var(--focus-color, #3b82f6);
      outline-offset: 2px;
    }

    .breadcrumb-text {
      font-weight: 500;
    }

    .breadcrumb-text--current {
      color: var(--text-primary, #111827);
      font-weight: 600;
    }

    .breadcrumb-icon {
      width: 1rem;
      height: 1rem;
      flex-shrink: 0;
    }

    .breadcrumb-separator {
      color: var(--text-muted, #9ca3af);
      font-weight: 400;
      user-select: none;
    }

    .breadcrumb-item--active .breadcrumb-text {
      color: var(--text-primary, #111827);
    }

    .breadcrumb-item--clickable {
      cursor: pointer;
    }

    .breadcrumb-back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      padding: 0;
      border: 1px solid var(--border-color, #d1d5db);
      border-radius: 0.375rem;
      background-color: var(--bg-primary, #ffffff);
      color: var(--text-secondary, #6b7280);
      cursor: pointer;
      transition: all 0.15s ease;
    }

    .breadcrumb-back-btn:hover {
      background-color: var(--bg-hover, #f3f4f6);
      border-color: var(--border-hover, #9ca3af);
      color: var(--text-primary, #111827);
    }

    .breadcrumb-back-btn:focus {
      outline: 2px solid var(--focus-color, #3b82f6);
      outline-offset: 2px;
    }

    .breadcrumb-back-btn:active {
      transform: translateY(1px);
    }

    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* Responsive design */
    @media (max-width: 640px) {
      .breadcrumb-nav {
        font-size: 0.8125rem;
        gap: 0.75rem;
      }
      
      .breadcrumb-list {
        gap: 0.375rem;
      }
      
      .breadcrumb-link {
        padding: 0.1875rem 0.375rem;
      }
      
      .breadcrumb-icon {
        width: 0.875rem;
        height: 0.875rem;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .breadcrumb-nav {
        color: #9ca3af;
      }
      
      .breadcrumb-link {
        color: #9ca3af;
      }
      
      .breadcrumb-link:hover {
        color: #60a5fa;
        background-color: #374151;
      }
      
      .breadcrumb-text--current {
        color: #f9fafb;
      }
      
      .breadcrumb-back-btn {
        border-color: #4b5563;
        background-color: #1f2937;
        color: #9ca3af;
      }
      
      .breadcrumb-back-btn:hover {
        background-color: #374151;
        border-color: #6b7280;
        color: #f9fafb;
      }
    }
  `]
})
export class BreadcrumbComponent {
  private readonly breadcrumbService = inject(BreadcrumbService);
  private readonly routeStateService = inject(RouteStateService);

  // ENHANCED: Angular 19+ input/output signals
  readonly separator = input<string>('/');
  readonly showBackButton = input<boolean>(true);
  readonly ariaLabel = input<string>('Breadcrumb navigation');
  readonly maxItems = input<number>(5);

  readonly breadcrumbClick = output<BreadcrumbItem>();
  readonly backClick = output<void>();

  // ENHANCED: Computed properties from services
  readonly breadcrumbs = this.breadcrumbService.breadcrumbs;
  readonly canGoBack = this.breadcrumbService.canGoBack;
  readonly routeConfig = this.routeStateService.getRouteConfig;

  /**
   * Handle breadcrumb click
   */
  onBreadcrumbClick(breadcrumb: BreadcrumbItem): void {
    this.breadcrumbClick.emit(breadcrumb);
    this.breadcrumbService.navigateTo(breadcrumb);
  }

  /**
   * Handle back button click
   */
  goBack(): void {
    this.backClick.emit();
    this.breadcrumbService.goBack();
  }

  /**
   * Get previous breadcrumb label for accessibility
   */
  getPreviousBreadcrumbLabel(): string {
    const crumbs = this.breadcrumbs();
    if (crumbs.length > 1) {
      return crumbs[crumbs.length - 2].label;
    }
    return 'previous page';
  }

  /**
   * Get truncated breadcrumbs if too many
   */
  getTruncatedBreadcrumbs(): BreadcrumbItem[] {
    const crumbs = this.breadcrumbs();
    const max = this.maxItems();
    
    if (crumbs.length <= max) {
      return crumbs;
    }
    
    // Keep first, last, and some middle items
    const first = crumbs[0];
    const last = crumbs[crumbs.length - 1];
    const middle = crumbs.slice(1, -1);
    
    if (middle.length <= max - 2) {
      return [first, ...middle, last];
    }
    
    // Add ellipsis item
    const ellipsis: BreadcrumbItem = {
      label: '...',
      isActive: false
    };
    
    const keepMiddle = max - 3; // Account for first, ellipsis, and last
    const startMiddle = middle.slice(0, Math.ceil(keepMiddle / 2));
    const endMiddle = middle.slice(-Math.floor(keepMiddle / 2));
    
    return [first, ...startMiddle, ellipsis, ...endMiddle, last];
  }
}
