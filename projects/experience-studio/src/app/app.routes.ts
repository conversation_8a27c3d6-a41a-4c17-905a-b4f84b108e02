import { Routes } from '@angular/router';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';
import { ProjectAccessGuard, ProjectOwnershipGuard, RouteValidationGuard } from './shared/guards/project-access.guard';
import { NavigationFlowGuard, UnsavedChangesGuard, RateLimitGuard } from './shared/guards/navigation-flow.guard';
import {
  projectResolver,
  projectAccessResolver,
  projectTypeResolver,
  routeMetadataResolver
} from './shared/resolvers/project.resolver';

export const routes: Routes = [
  {
    path: 'experience',
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
        data: { preload: true }
      },

      // ===== ENTERPRISE PROJECT ROUTES =====
      // Direct project access with UUID-based routing
      {
        path: 'projects/:projectId',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        resolve: {
          projectData: projectResolver,
          projectAccess: projectAccessResolver,
          projectType: projectTypeResolver,
          metadata: routeMetadataResolver
        },
        data: {
          routeType: 'project-direct',
          isProjectLoading: true,
          skipGuards: true,
          breadcrumb: 'Project'
        }
      },
      {
        path: 'projects/:projectId/edit',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        resolve: {
          projectData: projectResolver,
          projectAccess: projectAccessResolver,
          projectType: projectTypeResolver,
          metadata: routeMetadataResolver
        },
        data: {
          routeType: 'project-edit',
          isProjectLoading: true,
          skipGuards: true,
          breadcrumb: 'Edit Project'
        }
      },
      {
        path: 'projects/:projectId/share',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        resolve: {
          projectData: projectResolver,
          projectAccess: projectAccessResolver,
          projectType: projectTypeResolver,
          metadata: routeMetadataResolver
        },
        data: {
          routeType: 'project-share',
          isProjectLoading: true,
          skipGuards: true,
          breadcrumb: 'Share Project'
        }
      },

      // ===== UI DESIGN ROUTES =====
      {
        path: 'ui-design',
        children: [
          {
            path: 'new',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            canActivate: [CardSelectionGuard, NavigationFlowGuard, RateLimitGuard],
            canDeactivate: [UnsavedChangesGuard],
            resolve: {
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate UI Design',
              routeType: 'ui-design-new',
              breadcrumb: 'New UI Design'
            }
          },
          {
            path: ':projectId',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            canActivate: [RouteValidationGuard, ProjectAccessGuard, NavigationFlowGuard],
            canDeactivate: [UnsavedChangesGuard],
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate UI Design',
              routeType: 'ui-design-project',
              isProjectLoading: true,
              breadcrumb: 'UI Design Project'
            }
          },
          {
            path: ':projectId/preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate UI Design',
              routeType: 'ui-design-preview',
              isProjectLoading: true,
              skipGuards: true,
              breadcrumb: 'Preview UI Design'
            }
          },
          {
            path: ':projectId/edit',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            canActivate: [RouteValidationGuard, ProjectAccessGuard, ProjectOwnershipGuard, NavigationFlowGuard],
            canDeactivate: [UnsavedChangesGuard],
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate UI Design',
              routeType: 'ui-design-edit',
              isProjectLoading: true,
              breadcrumb: 'Edit UI Design'
            }
          }
        ]
      },

      // ===== APPLICATION ROUTES =====
      {
        path: 'application',
        children: [
          {
            path: 'new',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            canActivate: [CardSelectionGuard],
            data: {
              cardType: 'Generate Application',
              routeType: 'application-new',
              breadcrumb: 'New Application'
            }
          },
          {
            path: ':projectId',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate Application',
              routeType: 'application-project',
              isProjectLoading: true,
              skipGuards: true,
              breadcrumb: 'Application Project'
            }
          },
          {
            path: ':projectId/preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate Application',
              routeType: 'application-preview',
              isProjectLoading: true,
              skipGuards: true,
              breadcrumb: 'Preview Application'
            }
          },
          {
            path: ':projectId/edit',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            resolve: {
              projectData: projectResolver,
              projectAccess: projectAccessResolver,
              projectType: projectTypeResolver,
              metadata: routeMetadataResolver
            },
            data: {
              cardType: 'Generate Application',
              routeType: 'application-edit',
              isProjectLoading: true,
              skipGuards: true,
              breadcrumb: 'Edit Application'
            }
          }
        ]
      },

      // ===== LEGACY ROUTES - Keep for backward compatibility =====
      {
        path: 'prompt',
        redirectTo: 'application/new',
        pathMatch: 'full'
      },
      {
        path: 'code-preview',
        redirectTo: 'application/new',
        pathMatch: 'full'
      },
      {
        path: 'generate-application',
        children: [
          {
            path: 'prompt',
            redirectTo: '/experience/application/new',
            pathMatch: 'full'
          },
          {
            path: 'code-preview',
            redirectTo: '/experience/application/new',
            pathMatch: 'full'
          },
          {
            path: 'code-preview/projects/:projectId',
            redirectTo: '/experience/projects/:projectId',
            pathMatch: 'full'
          }
        ]
      },
      {
        path: 'generate-ui-design',
        children: [
          {
            path: 'prompt',
            redirectTo: '/experience/ui-design/new',
            pathMatch: 'full'
          },
          {
            path: 'code-preview',
            redirectTo: '/experience/ui-design/new',
            pathMatch: 'full'
          }
        ]
      },
      {
        path: 'project/:projectId',
        redirectTo: 'projects/:projectId',
        pathMatch: 'full'
      },

      // ===== DEMO ROUTES =====
      {
        path: 'project-loading-demo',
        loadComponent: () =>
          import('./shared/components/project-loading-demo/project-loading-demo.component').then(
            m => m.ProjectLoadingDemoComponent
          ),
        data: {
          routeType: 'demo',
          skipGuards: true,
          breadcrumb: 'Demo'
        }
      },
      {
        path: 'demo-nav',
        loadComponent: () =>
          import('./shared/components/project-loading-demo/demo-navigation.component').then(
            m => m.DemoNavigationComponent
          ),
        data: {
          routeType: 'demo',
          skipGuards: true,
          breadcrumb: 'Demo Navigation'
        }
      }
    ]
  },
  {
    path: '',
    redirectTo: 'experience/main',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'experience/main',
  },
];
